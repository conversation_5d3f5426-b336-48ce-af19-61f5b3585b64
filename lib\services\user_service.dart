import '../core/network/api_client.dart';
import '../models/user.dart';

final apiClient = ApiClient();

class UserService {
  
  /// Récupère tous les utilisateurs
  Future<List<User>> getAllUsers() async {
    final response = await apiClient.get<List<dynamic>>('/users');
    final List<dynamic> usersJson = response.data ?? [];
    return usersJson
        .map((userJson) => User.fromJson(userJson as Map<String, dynamic>))
        .toList();
  }

  /// Récupère un utilisateur par son ID
  Future<User> getUserById(int id) async {
    final response = await apiClient.get<Map<String, dynamic>>('/users/$id');
    if (response.data == null) {
      throw UserNotFoundException('User with ID $id not found');
    }
    return User.fromJson(response.data!);
  }

  /// Crée un nouvel utilisateur
  Future<User> createUser(CreateUserRequest userRequest) async {
    final response = await apiClient.post<Map<String, dynamic>>(
      '/users',
      data: userRequest.toJson(),
    );
    if (response.data == null) {
      throw UserServiceException('Failed to create user');
    }
    return User.fromJson(response.data!);
  }

  /// Met à jour complètement un utilisateur
  Future<User> updateUser(int id, CreateUserRequest userRequest) async {
    final response = await apiClient.put<Map<String, dynamic>>(
      '/users/$id',
      data: userRequest.toJson(),
    );
    if (response.data == null) {
      throw UserServiceException('Failed to update user');
    }
    return User.fromJson(response.data!);
  }

  /// Met à jour partiellement un utilisateur
  Future<User> patchUser(int id, Map<String, dynamic> updates) async {
    final response = await apiClient.put<Map<String, dynamic>>(
      '/users/$id',
      data: updates,
    );
    if (response.data == null) {
      throw UserServiceException('Failed to patch user');
    }
    return User.fromJson(response.data!);
  }

  /// Supprime un utilisateur
  Future<bool> deleteUser(int id) async {
    final response = await apiClient.delete('/users/$id');
    return response.statusCode == 200 || response.statusCode == 204;
  }

  /// Recherche des utilisateurs par nom ou email
  Future<List<User>> searchUsers(String query) async {
    final response = await apiClient.get<List<dynamic>>(
      '/users',
      queryParameters: {'q': query},
    );
    final List<dynamic> usersJson = response.data ?? [];
    final allUsers = usersJson
        .map((userJson) => User.fromJson(userJson as Map<String, dynamic>))
        .toList();
    // Filtre local par nom et email
    return allUsers.where((user) {
      return user.name.toLowerCase().contains(query.toLowerCase()) ||
          user.email.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// Récupère les utilisateurs avec pagination
  Future<UserListResponse> getUsersPaginated({
    int page = 1,
    int limit = 10,
  }) async {
    final response = await apiClient.get<List<dynamic>>(
      '/users',
      queryParameters: {'_page': page, '_limit': limit},
    );
    final List<dynamic> usersJson = response.data ?? [];
    final users = usersJson
        .map((userJson) => User.fromJson(userJson as Map<String, dynamic>))
        .toList();
    // Récupère le total depuis les headers ou utilise la taille actuelle
    final totalCount =
        int.tryParse(response.headers.value('x-total-count') ?? '0') ??
            users.length;
    return UserListResponse(
      users: users,
      totalCount: totalCount,
      page: page,
      limit: limit,
    );
  }
}

/// Réponse paginée pour la liste des utilisateurs
class UserListResponse {
  final List<User> users;
  final int totalCount;
  final int page;
  final int limit;

  const UserListResponse({
    required this.users,
    required this.totalCount,
    required this.page,
    required this.limit,
  });

  /// Vérifie s'il y a une page suivante
  bool get hasNextPage => (page * limit) < totalCount;

  /// Vérifie s'il y a une page précédente
  bool get hasPreviousPage => page > 1;

  /// Calcule le nombre total de pages
  int get totalPages => (totalCount / limit).ceil();
}

/// Exception générale du service utilisateur
class UserServiceException implements Exception {
  final String message;
  UserServiceException(this.message);
  @override
  String toString() => 'UserServiceException: $message';
}

/// Exception spécifique quand un utilisateur n'est pas trouvé
class UserNotFoundException extends UserServiceException {
  UserNotFoundException(super.message);
}
